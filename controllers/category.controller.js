const { Op } = require('sequelize');
const { STATUS_CODES } = require('../config/constants');
const { API } = require('../config/message');
const { deleteIconFile } = require('../helpers/icon.helper');

const CategoriesService = new (require('../services/categories.service'))();

class CategoryController {
  async getPaginatedCategories(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        // sortBy = 'is_pinned',
        // sortOrder = 'ASC',
        search = '',
      } = req.query;
      const filter = {};

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        // order: [[sortBy, sortOrder]],
      };

      if (search) {
        filter.category_name = { [Op.like]: `%${search.toLowerCase()}%` };
      }

      const result = await CategoriesService.findPaginatedCategories(
        filter,
        options
      );

      res.handler.custom(STATUS_CODES.SUCCESS, API.CATEGORIES_FETCHED, result);
    } catch (error) {
      console.log('Error fetching paginated categories:', error);

      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getAllCategories(_req, res) {
    try {
      const categories = await CategoriesService.findAllCategories();

      if (!categories || categories.length === 0) {
        return res.handler.custom(
          STATUS_CODES.NOT_FOUND,
          API.NO_CATEGORIES_FOUND
        );
      }

      const formattedCategories = categories.map(
        CategoriesService.categoryInstance
      );

      res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.CATEGORIES_FETCHED,
        formattedCategories
      );
    } catch (error) {
      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getActiveCategories(_req, res) {
    try {
      const categories = await CategoriesService.findAllCategories(
        {
          is_active: true,
        },
        {
          order: [
            // ['is_pinned', 'DESC'],
            ['category_priority', 'ASC'],
            ['category_name', 'ASC'],
          ],
        }
      );

      if (!categories || categories.length === 0) {
        return res.handler.custom(
          STATUS_CODES.NOT_FOUND,
          API.NO_CATEGORIES_FOUND
        );
      }

      const formattedCategories = categories.map(
        CategoriesService.categoryInstance
      );

      res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.CATEGORIES_FETCHED,
        formattedCategories
      );
    } catch (error) {
      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR);
    }
  }

  async getCategoryById(req, res) {
    try {
      const categoryId = req.params.id;
      const category = await this.categoryService.getCategoryById(categoryId);
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      res.status(200).json(category);
    } catch (error) {
      res.status(500).json({ message: 'Error fetching category', error });
    }
  }

  async updateCategory(req, res) {
    const filter = { id: req.body.categoryId };
    const data = {
      category_name: req.body.categoryName,
      is_active: req.body.isActive,
    };
    try {
      const result = await CategoriesService.updateCategory(filter, data);

      if (result.updated) {
        return res.handler.custom(STATUS_CODES.SUCCESS, API.CATEGORY_UPDATED);
      } else if (result.found === false) {
        return res.handler.custom(
          STATUS_CODES.NOT_FOUND,
          API.CATEGORY_NOT_FOUND
        );
      } else {
        return res.handler.custom(
          STATUS_CODES.SUCCESS,
          API.CATEGORY_ALREADY_UPDATED
        );
      }
    } catch (error) {
      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async uploadCategoryIcon(req, res) {
    const icon = req.file;
    const { categoryId } = req.body;

    const category = await CategoriesService.findCategory({ id: categoryId });

    if (!category) {
      return res.handler.custom(STATUS_CODES.NOT_FOUND, API.CATEGORY_NOT_FOUND);
    }

    const iconUrl = category.icon_url;

    if (iconUrl) {
      const iconName = iconUrl.split('/').pop();
      deleteIconFile(iconName);
    }

    const iconPath = `/assets/icons/${icon.filename}`;

    await CategoriesService.updateCategory(
      { id: categoryId },
      { icon_url: iconPath }
    );

    return res.handler.custom(STATUS_CODES.SUCCESS, API.CATEGORY_ICON_UPLOADED);
  }

  async assignCategoryPriority(req, res) {
    const { categoryId, priority } = req.body;

    try {
      const category = await CategoriesService.findCategory({ id: categoryId });

      if (!category) {
        return res.handler.custom(
          STATUS_CODES.NOT_FOUND,
          API.CATEGORY_NOT_FOUND
        );
      }

      await CategoriesService.updateCategory(
        { id: categoryId },
        { category_priority: priority }
      );

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.CATEGORY_PRIORITY_ASSIGNED
      );
    } catch (error) {
      console.error('Error assigning category priority:', error);
      return res.handler.custom(
        STATUS_CODES.SERVER_ERROR,
        API.SERVER_ERROR,
        error
      );
    }
  }
}

// async togglePinCategory(req, res) {
//   try {
//     const { categoryId, isPinned } = req.body;
//     const category = await CategoriesService.findCategory({ id: categoryId });

//     if (!category) {
//       return res.handler.custom(
//         STATUS_CODES.NOT_FOUND,
//         API.CATEGORY_NOT_FOUND
//       );
//     }

//     // const isPinned = !category.is_pinned;
//     await CategoriesService.updateCategory(
//       { id: categoryId },
//       { is_pinned: isPinned }
//     );

//     res.handler.custom(STATUS_CODES.SUCCESS, API.CATEGORY_UPDATED, {
//       is_pinned: isPinned,
//     });
//   } catch (error) {
//     res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
//   }
// }

module.exports = CategoryController;

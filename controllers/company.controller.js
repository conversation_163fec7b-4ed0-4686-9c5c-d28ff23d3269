const { Op } = require('sequelize');
const { STATUS_CODES, CONTACT_TYPES } = require('../config/constants');
const { API } = require('../config/message');
const CategoriesService = new (require('../services/categories.service'))();
const ContactNumbersService =
  new (require('../services/contactNumbers.service'))();

const CompaniesService = new (require('../services/companies.service'))();
const CompanyUrlsService = new (require('../services/companyUrls.service'))();
const { Category } = require('../database/schemas');
const { WebSocket } = require('ws');
const { fetchPhoneRatings } = require('../helpers/company.helper');

class CompanyController {
  async getPaginatedCompaniesByCategory(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'created_at',
        sortOrder = 'DESC',
        categoryId,
        search = '',
        lastDateTime,
      } = req.query;
      const filter = {};

      // Validate and set default sorting
      const validSortFields = [
        'id',
        'company_name',
        'created_at',
        'updated_at',
        'upvote_count',
        'downvote_count',
        'company_priority',
      ];
      const finalSortBy = validSortFields.includes(sortBy)
        ? sortBy
        : 'created_at';
      const finalSortOrder = ['ASC', 'DESC'].includes(sortOrder?.toUpperCase())
        ? sortOrder.toUpperCase()
        : 'DESC';

      const include = [
        {
          association: 'categories',
          required: !!categoryId,
          ...(categoryId && { where: { id: categoryId } }),
          order: [['id', 'ASC']],
          as: 'categories',
        },
        {
          association: 'contact_numbers',
          required: false,
          order: [['id', 'ASC']],
        },
        {
          association: 'company_urls',
          required: false,
          order: [['id', 'ASC']],
        },
      ];

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        order: [
          ['company_priority', 'ASC'],
          ['company_name', 'ASC'],
          [finalSortBy, finalSortOrder],
        ],
        include,
        logging: false,
      };

      if (search) {
        filter.company_name = { [Op.like]: `%${search.toLowerCase()}%` };
      }

      if (lastDateTime) {
        const lastDate = new Date(lastDateTime * 1000);
        if (isNaN(lastDate.getTime())) {
          return res.handler.custom(
            STATUS_CODES.BAD_REQUEST,
            API.INVALID_DATE_TIME
          );
        }
        filter.last_updated_at = { [Op.gt]: lastDate };
      }

      const category = await CategoriesService.findCategory({ id: categoryId });

      if (!category) {
        return res.handler.custom(
          STATUS_CODES.NOT_FOUND,
          API.CATEGORY_NOT_FOUND
        );
      }

      const result = await CompaniesService.findPaginatedCompanies(
        filter,
        options,
        categoryId
      );

      // Process the raw data to format companies with their first contact number
      const formattedCompanies = result.rawData.map((company) => {
        // Format the company using the companyInstance method
        const formattedCompany = CompaniesService.companyInstance(company);

        const groupedContacts = {
          TOLL_FREE: [],
          ALL_INDIA: [],
          INTERNATIONAL: [],
        };

        for (const contact of company.contact_numbers) {
          const formattedContact =
            ContactNumbersService.numberInstance(contact);
          switch (contact.contact_type) {
            case CONTACT_TYPES.TOLL_FREE:
              groupedContacts.TOLL_FREE.push(formattedContact);
              break;
            case CONTACT_TYPES.ALL_INDIA:
              groupedContacts.ALL_INDIA.push(formattedContact);
              break;
            case CONTACT_TYPES.INTERNATIONAL:
              groupedContacts.INTERNATIONAL.push(formattedContact);
              break;
          }
        }

        return {
          ...formattedCompany,
          categories: company.categories.map(
            CategoriesService.categoryInstance
          ),
          number: groupedContacts,
          companyUrls: company.company_urls.map(CompanyUrlsService.urlInstance),
        };
      });

      // Prepare the response following the established API pattern
      const response = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        companies: formattedCompanies,
      };

      res.handler.custom(STATUS_CODES.SUCCESS, API.COMPANIES_FETCHED, response);
    } catch (error) {
      console.log('Error fetching paginated companies:', error);

      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getAllPaginatedCompanies(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'created_at',
        sortOrder = 'DESC',
        search = '',
        lastDateTime,
      } = req.query;
      const filter = {};

      // Validate and set default sorting
      const validSortFields = [
        'id',
        'company_name',
        'created_at',
        'updated_at',
        'upvote_count',
        'downvote_count',
        'company_priority',
      ];
      const finalSortBy = validSortFields.includes(sortBy)
        ? sortBy
        : 'created_at';
      const finalSortOrder = ['ASC', 'DESC'].includes(sortOrder?.toUpperCase())
        ? sortOrder.toUpperCase()
        : 'DESC';

      const include = [
        {
          association: 'categories',
          required: false,
          order: [['id', 'ASC']],
          as: 'categories',
        },
        {
          association: 'contact_numbers',
          required: false,
          order: [['id', 'ASC']],
        },
        {
          association: 'company_urls',
          required: false,
          order: [['id', 'ASC']],
        },
      ];

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        // order: [[sortBy, sortOrder]],
        order: [
          ['company_priority', 'ASC'],
          ['company_name', 'ASC'],
          [finalSortBy, finalSortOrder],
        ],
        include,
        logging: false,
      };

      if (search) {
        filter.company_name = { [Op.like]: `%${search.toLowerCase()}%` };
      }

      if (lastDateTime) {
        const lastDate = new Date(lastDateTime * 1000);
        if (isNaN(lastDate.getTime())) {
          return res.handler.custom(
            STATUS_CODES.BAD_REQUEST,
            API.INVALID_DATE_TIME
          );
        }
        filter.last_updated_at = { [Op.gt]: lastDate };
      }

      const result = await CompaniesService.findPaginatedCompanies(
        filter,
        options
      );

      const formattedCompanies = result.rawData.map((company) => {
        // Format the company using the companyInstance method
        const formattedCompany = CompaniesService.companyInstance(company);

        // Add the first contact number if available
        const groupedContacts = {
          TOLL_FREE: [],
          ALL_INDIA: [],
          INTERNATIONAL: [],
        };

        for (const contact of company.contact_numbers) {
          const formattedContact =
            ContactNumbersService.numberInstance(contact);
          switch (contact.contact_type) {
            case CONTACT_TYPES.TOLL_FREE:
              groupedContacts.TOLL_FREE.push(formattedContact);
              break;
              1738215149;
            case CONTACT_TYPES.ALL_INDIA:
              groupedContacts.ALL_INDIA.push(formattedContact);
              break;
            case CONTACT_TYPES.INTERNATIONAL:
              groupedContacts.INTERNATIONAL.push(formattedContact);
              break;
          }
        }

        return {
          ...formattedCompany,
          categories: company.categories.map(
            CategoriesService.categoryInstance
          ),
          number: groupedContacts || null,
          companyUrls: company.company_urls.map(CompanyUrlsService.urlInstance),
        };
      });

      // Prepare the response following the established API pattern
      const response = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        companies: formattedCompanies,
      };

      res.handler.custom(STATUS_CODES.SUCCESS, API.COMPANIES_FETCHED, response);
    } catch (error) {
      console.log('Error fetching paginated companies:', error);

      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getCompanyById(req, res) {
    try {
      const { companyId } = req.params;

      const options = {
        include: [
          {
            association: 'categories',
            through: { attributes: [] },
          },
          {
            association: 'contact_numbers',
          },
        ],
      };

      // Find company with categories and contact numbers
      const company = await CompaniesService.findCompany(
        {
          id: companyId,
        },
        options
      );

      if (!company) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.NOT_FOUND, null);
      }

      const groupedContacts = {
        TOLL_FREE: [],
        ALL_INDIA: [],
        INTERNATIONAL: [],
      };

      for (const contact of company.contact_numbers) {
        const formattedContact = ContactNumbersService.numberInstance(contact);
        switch (contact.contact_type) {
          case CONTACT_TYPES.TOLL_FREE:
            groupedContacts.TOLL_FREE.push(formattedContact);
            break;
          case CONTACT_TYPES.ALL_INDIA:
            groupedContacts.ALL_INDIA.push(formattedContact);
            break;
          case CONTACT_TYPES.INTERNATIONAL:
            groupedContacts.INTERNATIONAL.push(formattedContact);
            break;
        }
      }

      const formattedCompany = {
        ...CompaniesService.companyInstance(company),
        categories:
          company.categories?.map(CategoriesService.categoryInstance) || [],
        contactNumbers: groupedContacts || [],
      };

      res.handler.custom(STATUS_CODES.SUCCESS, API.SUCCESS, formattedCompany);
    } catch (error) {
      console.log('Error fetching company details:', error);
      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getContactUpDown(req, res) {
    console.log('Starting getContactUpDown process');

    try {
      const companyId = req.body.companyId;
      if (!companyId) {
        return res.handler.custom(400, 'Company ID is required', null);
      }

      const company = await CompaniesService.findCompany(
        { id: companyId },
        { include: [{ association: 'contact_numbers' }], logging: false }
      );

      if (!company) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.NOT_FOUND, null);
      }

      const contacts = company.contact_numbers || [];
      if (contacts.length === 0) {
        return res.handler.success({
          company_id: companyId,
          contactsVotes: [],
        });
      }

      console.log(`Processing ${contacts.length} contact numbers`);

      // Fetch up-down votes using WebSocket mechanism
      const votesData = await fetchPhoneRatings(contacts);

      // Update votes in database if we have valid data
      const validVotes = votesData.filter((v) => !v.error);
      if (validVotes.length > 0) {
        await ContactNumbersService.bulkUpdateVotes(validVotes);
      }

      return res.handler.success({
        companyId: companyId,
        contactsVotes: votesData,
      });
    } catch (error) {
      console.error('Error in getContactUpDown:', error);
      return res.handler.custom(500, 'Server Error', error.message);
    }
  }

  async assignCompanyPriority(req, res) {
    try {
      const { companyId, priority } = req.body;

      const company = await CompaniesService.findCompany({ id: companyId });

      if (!company) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.NOT_FOUND);
      }

      // Update the company priority
      await CompaniesService.updateCompany(
        { id: companyId },
        { company_priority: priority }
      );

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.COMPANY_PRIORITY_UPDATED,
        { companyId, priority }
      );
    } catch (error) {
      console.error('Error assigning company priority:', error);
      return res.handler.custom(
        STATUS_CODES.SERVER_ERROR,
        API.SERVER_ERROR,
        error
      );
    }
  }
}

module.exports = CompanyController;

// const requestPayload = {
//   l: 'p4n0jt8njtg3', // License key from client.js
//   op: 'readratings', // Operation type
//   no: uniquePhones, // Array of phone numbers
//   url: 'server-side-request', // Identifier for server requests
// };
const { WebSocket } = require('ws');

async function fetchPhoneRatings(contacts) {
  return new Promise((resolve) => {
    const websocket = new WebSocket(
      'wss://phoneratings.tariffplansindia.com:8001'
    );
    const responseData = {};
    let connectionClosed = false;
    let expectedResponses = contacts.length;
    let receivedResponses = 0;

    // Global timeout for the entire operation
    const timeout = setTimeout(() => {
      console.log('WebSocket operation timeout reached');
      finalize('Request timeout');
    }, 30000); // 30 seconds timeout

    function finalize(defaultError = null) {
      if (connectionClosed) return;
      connectionClosed = true;
      clearTimeout(timeout);

      try {
        if (websocket.readyState === WebSocket.OPEN) {
          websocket.close();
        }
      } catch (err) {
        console.error('Error closing WebSocket:', err);
      }

      // Process results for all contacts
      const results = contacts.map((contact) => {
        const phoneNumber = contact.contact_number.replace(/\s/g, '').trim();
        const resp = responseData[phoneNumber];

        if (resp && !resp.error) {
          return {
            contactId: contact.id,
            contactNumber: contact.contact_number,
            upVotes: Math.abs(resp.up) || 0,
            downVotes: Math.abs(resp.down) || 0,
          };
        } else {
          return {
            contactId: contact.id,
            contactNumber: contact.contact_number,
            upVotes: 0,
            downVotes: 0,
            error: defaultError || resp?.error || 'No response received',
          };
        }
      });

      resolve(results);
    }

    websocket.onopen = () => {
      console.log('WebSocket connected to phone ratings service');

      // Prepare phone numbers array (similar to client.js logic)
      const phoneNumbers = contacts.map((contact) =>
        contact.contact_number.replace(/\s/g, '').trim()
      );

      // Remove duplicates while maintaining contact mapping
      const uniquePhones = [...new Set(phoneNumbers)];

      // Send request payload matching client.js format
      const requestPayload = {
        l: 'asjflasjflasj', // License key from client.js
        op: 'sfkljasklfj', // Operation type
        no: uniquePhones, // Array of phone numbers
        url: 'lsajflasjflsajlkj', // Identifier for server requests
      };

      try {
        websocket.send(JSON.stringify(requestPayload));
        console.log(
          `Sent ratings request for ${uniquePhones.length} unique phone numbers`
        );
      } catch (err) {
        console.error('WebSocket send error:', err);
        finalize('Failed to send request');
      }
    };

    websocket.onmessage = (event) => {
      try {
        const response = JSON.parse(event.data);
        console.log('Received ratings response:', response);

        // Handle response format similar to client.js
        if (typeof response.notfound !== 'undefined' && response.notfound) {
          // Numbers not found in database - initialize with 0 ratings
          if (response.no && Array.isArray(response.no)) {
            response.no.forEach((phoneNumber) => {
              responseData[phoneNumber] = { up: 0, down: 0 };
            });
          }
        } else if (Array.isArray(response)) {
          // Numbers found - process the ratings data
          response.forEach((ratingObj) => {
            if (ratingObj.no) {
              responseData[ratingObj.no] = {
                up: ratingObj.up || 0,
                down: ratingObj.down || 0,
              };
            }
          });
        } else if (response.up !== undefined || response.down !== undefined) {
          // Single number response format
          const phoneNumbers = contacts.map((c) =>
            c.contact_number.replace(/\s/g, '').trim()
          );
          if (phoneNumbers.length === 1) {
            responseData[phoneNumbers[0]] = {
              up: response.up || 0,
              down: response.down || 0,
            };
          }
        }

        // Check if we have responses for all numbers
        const uniquePhones = [
          ...new Set(
            contacts.map((c) => c.contact_number.replace(/\s/g, '').trim())
          ),
        ];

        const completedCount = uniquePhones.filter(
          (phone) => responseData[phone] !== undefined
        ).length;

        if (completedCount >= uniquePhones.length) {
          finalize();
        }
      } catch (err) {
        console.error('Error parsing WebSocket response:', err);
        finalize('Response parsing error');
      }
    };

    websocket.onerror = (err) => {
      console.error('WebSocket error:', err);
      finalize('WebSocket connection error');
    };

    websocket.onclose = (event) => {
      console.log(`WebSocket closed: ${event.code} - ${event.reason}`);
      if (!connectionClosed) {
        finalize('Connection closed unexpectedly');
      }
    };
  });
}

module.exports = { fetchPhoneRatings };

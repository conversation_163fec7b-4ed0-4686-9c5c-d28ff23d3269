exports.API = {
  SUCCESS: 'Success',
  BAD_REQUEST: 'Bad Request',
  CREATED: 'Created',
  UNAUTHORIZED: 'Unauthorized',
  FORBIDDEN: 'Forbidden',
  NOT_FOUND: 'Not Found',
  CONFLICT: 'Conflict',
  PRECONDITION_FAILED: 'Precondition Failed',
  VALIDATION_ERROR: 'Validation Error',
  SERVER_ERROR: 'Something broke on the Server...',
  NOT_ALLOWED: 'Not Allowed',
  SERVICE_UNAVAILABLE: 'Service Unavailable',
  INVALID_TOKEN: 'Invalid Token!',
  TOKEN_EXPIRED: 'Token Expired!',
  // EMAIL_EXISTS: 'Email already exist.',
  // PHONE_NUMBER_EXISTS: 'Phone number already exist.',
  // REGISTER_SUCCESS:
  //   'Account registered successfully. Please verify your account.',
  // USER_DELETED: 'Account was deleted.',
  // USER_UNVERIFIED: 'O<PERSON> has been successfully sent.',
  // USER_NOT_VERIFIED: 'User not verified!',
  // USER_INACTIVE:
  //   'Your account has been inactivated. Please contact admin to active it.',
  USER_INVALID_CREDENTIALS: 'Invalid credentials!',
  LOGIN_FAILED: 'Invalid email or password!',
  LOGIN_SUCCESS: 'Login successful!',
  LOGIN_INACTIVE:
    'Your account has been inactivated by admin. Please contact admin to active it.',
  INVALID_AUTH_TOKEN: 'Unauthorized',
  PROFILE_SUCCESS: 'Profile has been retrieved successfully!',
  CHANGE_PASSWORD_FAILED:
    'Current password does not match with your existing password.',
  CHANGE_PASSWORD_SUCCESS: 'Your password has been successfully changed.',
  UPDATE_PROFILE_SUCCESS: 'Your profile has been successfully updated.',
  FORGOT_PASSWORD_FAILED: 'Email does not exists.',
  USER_NORMAL_PASSWORD_CHANGE:
    'You can only change password in normal account.',
  USER_GOOGLE_EXISTS: 'User already exists with google account.',
  USER_NORMAL_EXISTS: 'User already exists using normal login with this email.',
  FORGOT_PASSWORD_SUCCESS: 'Forgot password otp has been successfully sent.',
  EXPIRED_OTP: 'OTP has been expired.',
  INVALID_OTP: 'Invalid OTP.',
  RESET_PASSWORD_SUCCESS: 'Your password has been successfully reset.',
  PASSWORD_NOT_MATCH: 'Password and confirm password does not match.',
  ADMIN_NOT_FOUND: 'Admin not found!',
  USER_NOT_FOUND: 'User not found!',
  // SETTING_SUCCESS: 'Settings has been successfully updated.',
  // USER_ALREADY_VERIFIED: 'User already verified.',
  // USER_VERIFIED_SUCCESS: 'Your account has been successfully verified.',
  // RESEND_VERIFICATION_SUCCESS:
  //   'Resend verification otp has been sent successfully.',
  LOGOUT_SUCCESS: 'Logged out successfully.',
  USERS_LIST_SUCCESS: 'Users list fetched successfully.',
  USER_DETAILS_SUCCESS: 'Users details fetched successfully.',
  USER_ACTIVE_SUCCESS: 'User activated successfully.',
  USER_INACTIVE_SUCCESS: 'User inactivated successfully.',
  USER_ACTIVE_INACTIVE_ERROR:
    'Something went wrong while active/inactive user.Please Try Again!',
  USER_DELETE_SUCCESS: 'User deleted successfully.',
  USER_DELETE_ERROR:
    'Something went wrong while deleting user.Please Try Again!',
  NO_CATEGORIES_FOUND: 'No categories found',
  CATEGORIES_FETCHED: 'Categories retrieved successfully',
  COMPANIES_FETCHED: 'Companies fetched successfully',
  CATEGORY_NOT_FOUND: 'Requested Category not found',
  CATEGORY_ICON_UPLOADED: 'Category icon uploaded successfully',
  CATEGORY_UPDATED: 'Category Updated Successfully.',
  CATEGORY_ALREADY_UPDATED: 'Category already updated.',
  CATEGORY_NOT_FOUND: 'Category not found.',
  CATEGORY_PRIORITY_ASSIGNED: 'Category priority assigned successfully.',
  NO_STATIC_PAGES_FOUND: 'No static pages found',
  STATIC_PAGE_NOT_FOUND: 'Static page not found',
  STATIC_PAGE_CREATED: 'Static page created successfully',
  STATIC_PAGE_UPDATED: 'Static page updated successfully',
  STATIC_PAGE_DELETED: 'Static page deleted successfully',
  STATIC_PAGES_FETCHED: 'Static pages fetched successfully',
};

module.exports = (sequelize, DataTypes) => {
  const Category = sequelize.define(
    'Category',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      cat_id: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
      },
      category_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      icon_url: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      category_priority: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 999999,
      },
      // is_pinned: {
      //   type: DataTypes.BOOLEAN,
      //   allowNull: false,
      //   defaultValue: false,
      // },
    },
    {
      tableName: 'categories',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Associations
  Category.associate = (models) => {
    Category.belongsToMany(models.Company, {
      through: models.CompanyCategory,
      foreignKey: 'category_id',
      otherKey: 'company_id',
    });

    Category.hasMany(models.CompanyCategory, {
      foreignKey: 'category_id',
      as: 'company_categories',
    });
  };

  return Category;
};

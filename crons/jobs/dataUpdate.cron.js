const { default: axios } = require('axios');
const {
  processJsonDataHelper,
  splitArrayIntoChunks,
} = require('../../helpers/data.helper');
const { DATA_UPDATE_URL } = require('../../config/constants');
const { sequelize } = require('../../database/schemas');
const { Transaction } = require('sequelize');
const CompanyCategoryService =
  new (require('../../services/companyCategory.service'))();

const CompaniesService = new (require('../../services/companies.service'))();
const CategoriesService = new (require('../../services/categories.service'))();
const ContactNumbersService =
  new (require('../../services/contactNumbers.service'))();
const CompanyUrlsService =
  new (require('../../services/companyUrls.service'))();

class DataUpdateCron {
  async execute() {
    console.time('Data Update Cron Took Time (ms): ')
    try {
      const jsonData = await this.fetchJsonData();

      const processedData = processJsonDataHelper(jsonData);

      await this.updateDatabase(processedData);
    } catch (error) {
      console.error('Error in data update cron job:', error);
    }
    console.timeEnd('Data Update Cron Took Time (ms): ')
  }

  async fetchJsonData() {
    try {
      const response = await axios.get(DATA_UPDATE_URL);
      return response.data;
    } catch (error) {
      throw new Error('Error fetching data: ' + error.message);
    }
  }

  async updateDatabase(chunk) {
    console.log('chunk: ', chunk.length);

    const transaction = await sequelize.transaction({
      isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    });

    try {
      /* Find All the existing  */

      const chunkCompIds = new Set();
      const chunkCatIds = new Set();

      chunk.forEach((item) => {
        chunkCompIds.add(item.company.id.toString());

        item.category.forEach((cat) => {
          chunkCatIds.add(cat.id.toString());
        });
      });

      const existingCompanies = await CompaniesService.findAllCompanies(
        {
          // comp_id: Array.from(chunkCompIds.values()),
        },
        {
          transaction,
          attributes: ['id', 'comp_id'],
          logging: false,
        }
      );

      const existingCategories = await CategoriesService.findAllCategories(
        {
          // cat_id: Array.from(chunkCatIds.values()),
        },
        {
          transaction,
          attributes: ['id', 'cat_id'],
          logging: false,
        }
      );

      // const companyIdMap = new Map(
      //   existingCompanies.map((company) => [company.comp_id, company.id])
      // );

      // const companyContactsIds = existingCompanies.map((company) => company.id);

      // const existingContacts = await ContactNumbersService.findAllNumbers(
      //   {
      //     company_id: companyContactsIds,
      //   },
      //   {
      //     transaction,
      //     logging: false,
      //   }
      // );

      // const existingCompanyUrls = await CompanyUrlsService.findAllUrls(
      //   {
      //     company_id: companyContactsIds,
      //   },
      //   {
      //     transaction,
      //     logging: false,
      //   }
      // );

      // const incomingContacts = new Map();
      // const incomingUrls = new Map();

      // chunk.forEach((item) => {
      //   // Get the database company ID using the incoming comp_id
      //   const dbCompanyId = companyIdMap.get(item.company.id);

      //   if (dbCompanyId) {
      //     // Store contacts as "dbCompanyId:number" for unique checking
      //     item.contact.forEach((c) => {
      //       incomingContacts.set(`${dbCompanyId}:${c.number}`, true);
      //     });

      //     // Store URLs as "dbCompanyId:url" for unique checking
      //     item.url.forEach((u) => {
      //       incomingUrls.set(`${dbCompanyId}:${u.url}`, true);
      //     });
      //   }
      // });

      /* Mapping All the Ids To Delete */

      // Find companies that exist in DB but not in the new data
      const companiesToDelete = existingCompanies
        .filter((company) => !chunkCompIds.has(company.comp_id.toString()))
        .map((company) => company.id);

      const categoriesToDelete = existingCategories
        .filter((category) => !chunkCatIds.has(category.cat_id.toString()))
        .map((category) => category.id);

      // // Find contacts to delete
      // const contactsToDelete = existingContacts
      //   .filter((contact) => {
      //     const contactKey = `${contact.company_id}:${contact.contact_number}`;
      //     return !incomingContacts.has(contactKey);
      //   })
      //   .map((contact) => contact.id);

      // // Find URLs to delete
      // const urlsToDelete = existingCompanyUrls
      //   .filter((url) => {
      //     const urlKey = `${url.company_id}:${url.url}`;
      //     return !incomingUrls.has(urlKey);
      //   })
      //   .map((url) => url.id);

      /* Deleting All the unnecessary Data */

      if (companiesToDelete.length > 0) {
        console.log('Companies To Delete: ', companiesToDelete.length);

        await CompaniesService.deleteCompany(
          { id: companiesToDelete },
          {
            transaction,
            logging: false,
          }
        );
      }

      if (categoriesToDelete.length > 0) {
        console.log('Categories To Delete: ', categoriesToDelete.length);

        await CategoriesService.deleteCategory(
          { id: categoriesToDelete },
          {
            transaction,
            logging: false,
          }
        );
      }

      await ContactNumbersService.truncateAll(transaction);
      await CompanyUrlsService.truncateAll(transaction);
      await CompanyCategoryService.truncateAll(transaction);

      // if (contactsToDelete.length > 0) {
      //   console.log('Contacts To Delete: ', contactsToDelete.length);
      //   await ContactNumbersService.deleteNumber(
      //     { id: contactsToDelete },
      //     {
      //       transaction,
      //       logging: false,
      //     }
      //   );
      // }

      // if (urlsToDelete.length > 0) {
      //   console.log('URLs To Delete: ', urlsToDelete.length);
      //   await CompanyUrlsService.deleteUrl(
      //     { id: urlsToDelete },
      //     {
      //       transaction,
      //       logging: false,
      //     }
      //   );
      // }

      // for (const data of chunk) {
      
      const uniqueCategories = new Map();

      // console.log('data: ', data);

      chunk.forEach((companyObj) => {
        companyObj.category.forEach((cat) => {
          uniqueCategories.set(cat.id, {
            cat_id: cat.id,
            category_name: cat.category_name,
            icon_url: cat.icon_url || null,
          });
        });
      });

      console.log('Processing Categories...');

      const categoryMap = new Map();

      // console.log('uniqueCategories: ', uniqueCategories.length);

      // if (uniqueCategories.size > 0) {
      //   const categoriesArray = Array.from(uniqueCategories.values());

      //   const categoryEntries = await CategoriesService.bulkCreateCategory(
      //     categoriesArray,
      //     {
      //       transaction,
      //       updateOnDuplicate: ['category_name'],
      //       fields: ['id', 'cat_id', 'category_name', 'icon_url'],
      //       upsertKeys: ['cat_id'],
      //       returning: true,
      //       logging: false,
      //     }
      //   );

      //   console.log('Total Categories: ', categoryEntries[0]);

      //   categoryEntries.forEach((cat) => {
      //     categoryMap.set(cat.cat_id, cat);
      //   });
      // }

      if (uniqueCategories.size > 0) {
        const categoriesArray = Array.from(uniqueCategories.values());

        // First find existing categories
        const existingCats = await CategoriesService.findAllCategories(
          {
            cat_id: categoriesArray.map((cat) => cat.cat_id),
          },
          {
            transaction,
            // attributes: ['id', 'cat_id', 'category_name'],
            logging: false
          }
        );

        // Create a map of existing categories
        const existingCatsMap = new Map(
          existingCats.map((cat) => [cat.cat_id, cat])
        );

        // Update categoryEntries creation
        const categoryEntries = await CategoriesService.bulkCreateCategory(
          categoriesArray,
          {
            transaction,
            updateOnDuplicate: ['category_name'],
            fields: ['cat_id', 'category_name', 'icon_url'],
            returning: true,
            individualHooks: true, 
            logging: false, 
          }
        );

        // Clear and rebuild category map with proper IDs
        categoryMap.clear();
        categoryEntries.forEach((cat) => {
          if (!cat.id) {
            // If new category doesn't have ID, try to get from existing
            const existingCat = existingCatsMap.get(cat.cat_id);
            if (existingCat) {
              cat.id = existingCat.id;
            }
          }

          if (!cat.id) {
            console.warn('Category still missing ID:', {
              cat_id: cat.cat_id,
              category_name: cat.category_name,
            });
            return;
          }

          categoryMap.set(cat.cat_id, cat);
        });

        console.log('Categories mapped:', categoryMap.size);
      }

      console.log('Categories Inserted Successfully');

      const companyPayloads = chunk.map((companyObj) => ({
        comp_id: companyObj.company.id,
        company_name: companyObj.company.company_name,
        parent_company: companyObj.company.parent_company || null,
        company_email: companyObj.company.company_email || null,
        company_logo_url: companyObj.company.company_logo_url || null,
        company_country: companyObj.company.company_country || null,
        company_address: companyObj.company.company_address || null,
        company_website: companyObj.company.company_website || null,
        last_updated_at:
          new Date(companyObj.company.last_updated_at * 1000).toUTCString() ||
          null,
      }));

      // Bulk create companies
      await CompaniesService.bulkCreateCompanies(companyPayloads, {
        transaction,
        updateOnDuplicate: [
          'company_name',
          'parent_company',
          'company_email',
          'company_logo_url',
          'company_country',
          'company_address',
          'company_website',
          'last_updated_at',
          'upvote_count',
          'downvote_count',
        ],
        fields: [
          'comp_id',
          'company_name',
          'parent_company',
          'company_email',
          'company_logo_url',
          'company_country',
          'company_address',
          'company_website',
          'last_updated_at',
          'upvote_count',
          'downvote_count',
        ],
        logging: false,
      });

      const createdCompanies = await CompaniesService.findAllCompanies(
        {},
        {
          transaction,
          logging: false,
        }
      );

      createdCompanies.forEach((company) => {
        if (!company.id) {
          console.log('company: ', company);
        }
      });

      console.log('Total Companies: ', createdCompanies.length);

      const contactPayloads = [];
      const categoryLinkPayloads = [];
      const urlPayloads = [];

      createdCompanies.forEach((companyInstance, index) => {
        const companyId = companyInstance.id;
        const { contact, category, url } = chunk[index];

        // Contact numbers
        contact.forEach((c) => {
          contactPayloads.push({
            company_id: companyId,
            contact_number: c.number,
            contact_description: c.contact_description || null,
            contact_type: c.contact_type,
            is_whatsapp: c.is_whatsapp,
          });
        });

        // console.log('categoryMap: ', categoryMap);

        // Category links
        category.forEach((cat) => {
          const categoryInstance = categoryMap.get(cat.id);
          // console.log(categoryInstance);

          if (categoryInstance) {
            categoryLinkPayloads.push({
              company_id: companyId,
              category_id: categoryInstance.id,
            });
          }
        });

        // URLs
        url.forEach((u) => {
          urlPayloads.push({
            company_id: companyId,
            url: u.url,
            url_type: u.url_type,
          });
        });
      });

      // Process related data in smaller chunks
      const RELATED_BATCH_SIZE = 100; // Reduced from 100

      if (categoryLinkPayloads.length > 0) {
        for (
          let j = 0;
          j < categoryLinkPayloads.length;
          j += RELATED_BATCH_SIZE
        ) {
          // console.log('categoryLinkPayloads: ', categoryLinkPayloads);

          // await CompanyCategoryService.findOrCreateLinks(
          //   categoryLinkPayloads.slice(j, j + RELATED_BATCH_SIZE),
          //   { transaction, logging: false }
          // );
          await CompanyCategoryService.bulkCreateLinks(
            categoryLinkPayloads.slice(j, j + RELATED_BATCH_SIZE),
            {
              transaction,
              updateOnDuplicate: ['category_id'],
              fields: ['company_id', 'category_id'],
              upsertKeys: ['company_id', 'category_id'],
              logging: false,
            }
          );

          console.log(
            'Processing Company Category Batch No Starting From: ',
            j
          );
        }
        console.log('Completed Processing Company-Category Relation.');
      }

      if (contactPayloads.length > 0) {
        for (let j = 0; j < contactPayloads.length; j += RELATED_BATCH_SIZE) {
          await ContactNumbersService.bulkCreateContactNumbers(
            contactPayloads.slice(j, j + RELATED_BATCH_SIZE),
            {
              transaction,
              updateOnDuplicate: [
                'contact_description',
                'contact_type',
                'is_whatsapp',
              ],
              fields: [
                'company_id',
                'contact_number',
                'contact_description',
                'contact_type',
                'is_whatsapp',
              ],
              upsertKeys: ['company_id', 'contact_number'],
              logging: false,
            }
          );
          console.log('Processing Contact Number Batch Starting From: ', j);
        }
        console.log('Completed Inserting Contact Numbers...');
      }

      if (urlPayloads.length > 0) {
        for (let j = 0; j < urlPayloads.length; j += RELATED_BATCH_SIZE) {
          await CompanyUrlsService.bulkCreateUrls(
            urlPayloads.slice(j, j + RELATED_BATCH_SIZE),
            {
              transaction,
              updateOnDuplicate: ['url', 'url_type'],
              fields: ['company_id', 'url', 'url_type'],
              upsertKeys: ['url', 'company_id'],
              logging: false,
            }
          );
          console.log('Processing URL Batch No Starting From: ', j);
        }
        console.log('Completed Inserting Company URLs...');
      }
      // }

      transaction.commit();
    } catch (error) {
      transaction.rollback();
      throw error;
    }
  }
}

module.exports = DataUpdateCron;

const { API } = require('../config/message');
const { Category, CompanyCategory, sequelize } = require('../database/schemas');
const { getIconUrl } = require('../helpers/icon.helper');

class CategoriesService {
  categoryInstance(category) {
    // Process the icon URL to ensure it's a full URL
    const iconUrl = category.icon_url
      ? getIconUrl(category.icon_url)
      : getIconUrl('default');

    return {
      categoryId: category.id,
      catId: category.cat_id,
      name: category.category_name || null,
      iconUrl: iconUrl,
      isActive: category.is_active,
      categoryPriority: category.category_priority,
      // isPinned: category.is_pinned,
    };
  }

  async findCategory(filter, options = {}) {
    return await Category.findOne({ where: filter, raw: false, ...options });
  }

  async findAllCategories(filter = {}, options = {}) {
    return await Category.findAll({ where: filter, ...options });
  }

  async findPaginatedCategories(filter, options = {}) {
    const {
      page = 1,
      limit = 10,
      order = [
        // ['is_pinned', 'DESC'],
        ['category_priority', 'ASC'],
        ['category_name', 'ASC'],
        ['id', 'DESC'],
      ],
    } = options;
    const offset = (page - 1) * limit;

    const { count, rows } = await Category.findAndCountAll({
      where: filter,
      order,
      attributes: [
        'id',
        'cat_id',
        'category_name',
        'icon_url',
        'is_active',
        // 'is_pinned',
        'category_priority',
      ],
      limit,
      offset,
      ...options,
    });

    return {
      total: count,
      page,
      limit,
      categories: rows.map(this.categoryInstance),
    };
  }

  async createCategory(data) {
    return await Category.create(data);
  }

  async bulkCreateCategory(data, options = {}) {
    return await Category.bulkCreate(data, options);
  }

  async updateCategory(filter, data) {
    const transaction = await sequelize.transaction();
    try {
      const category = await this.findCategory(filter, { transaction });

      if (!category) {
        await transaction.rollback();
        return { updated: false, found: false };
      }

      category.set(data);

      if (category.changed()) {
        await category.save({ transaction });
        await transaction.commit();
        return { updated: true, changedFields: category.changed() };
      } else {
        await transaction.commit();
        return { updated: false };
      }
    } catch (error) {
      if (!transaction.finished) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async deleteCategory(filter, options = {}) {
    return await Category.destroy({ where: filter, ...options });
  }

  async findOrCreateCategories(categories, transaction) {
    const result = [];
    for (const cat of categories) {
      const [category] = await Category.findOrCreate({
        where: { category_name: cat.category_name },
        defaults: { ...cat },
        transaction,
      });
      result.push(category);
    }
    return result;
  }

  async findOrCreateCategoriesBulk(categories, transaction) {
    // First find all existing categories
    const categoryNames = categories.map((c) => c.category_name);
    const existingCategories = await Category.findAll({
      where: {
        category_name: categoryNames,
      },
      transaction,
    });

    // Determine which categories need to be created
    const existingCategoryNames = new Set(
      existingCategories.map((c) => c.category_name)
    );
    const categoriesToCreate = categories.filter(
      (c) => !existingCategoryNames.has(c.category_name)
    );

    // Create missing categories
    let newCategories = [];
    if (categoriesToCreate.length > 0) {
      newCategories = await Category.bulkCreate(categoriesToCreate, {
        transaction,
        returning: true,
      });
    }

    // Return all categories (existing + newly created)
    return [...existingCategories, ...newCategories];
  }

  async linkCategoriesToCompany(companyId, categories, transaction) {
    const data = categories.map((c) => ({
      company_id: companyId,
      category_id: c.id,
    }));
    await CompanyCategory.bulkCreate(data, { transaction });
  }

  async truncateAll(transaction) {
    await CompanyCategory.destroy({
      where: {},
      truncate: true,
      cascade: true,
      transaction,
    });
    // Uncomment if you also want to truncate the Category table
    await Category.destroy({
      where: {},
      truncate: false,
      cascade: true,
      transaction,
    });
  }

  async bulkCreateLinks(links, options = {}) {
    return await CompanyCategory.bulkCreate(links, options);
  }
}

module.exports = CategoriesService;

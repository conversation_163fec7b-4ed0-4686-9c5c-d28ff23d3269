const { query, body } = require('express-validator');
const { headerValidator } = require('./common.validator');

exports.getAllCategoryValidator = [...headerValidator];

exports.getPaginatedCategoriesValidator = [
  ...headerValidator,
  query('page', 'Please provide page number.')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page number must be greater than 0'),
  query('limit', 'Please provide limit number.')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Limit number must be positive'),
  // query('sortBy', 'Please provide sortBy field.').optional().isString(),
  // query('sortOrder', 'Please provide sortOrder field.')
  //   .optional()
  //   .isString()
  //   .isIn(['ASC', 'DESC']),
  query('search', 'Please provide search term.').optional().isString(),
];

exports.uploadCategoryIconValidator = [
  ...headerValidator,
  body('categoryId')
    .trim()
    .notEmpty()
    .withMessage('category id is required.')
    .isInt({ min: 1 })
    .withMessage('Category Id must be greater than 0.'),
];

// exports.togglePinCategoryValidator = [
//   ...headerValidator,
//   body('categoryId')
//     .trim()
//     .notEmpty()
//     .withMessage('category id is required.')
//     .isInt({ min: 1 })
//     .withMessage('Category Id must be greater than 0.'),
//   body('isPinned')
//     .trim()
//     .notEmpty()
//     .withMessage('isPinned status is required.')
//     .isBoolean()
//     .withMessage('isPinned status must be a boolean.'),
// ];

exports.updateCategoryValidator = [
  ...headerValidator,
  body('categoryId')
    .trim()
    .notEmpty()
    .withMessage('category id is required.')
    .isInt({ min: 1 })
    .withMessage('Category Id must be greater than 0.'),
  body('categoryName')
    .trim()
    .notEmpty()
    .withMessage('category name is required.')
    .isString()
    .withMessage('Category name must be a string.'),
  body('isActive')
    .trim()
    .notEmpty()
    .withMessage('Category Status is required.')
    .isBoolean()
    .withMessage('Category status must be a boolean.'),
];

exports.assignCategoryPriorityValidator = [
  ...headerValidator,
  body('categoryId')
    .trim()
    .notEmpty()
    .withMessage('category id is required.')
    .isInt({ min: 1 })
    .withMessage('Category Id must be greater than 0.'),
  body('priority')
    .trim()
    .notEmpty()
    .withMessage('Priority is required.')
    .isInt({ min: 0 })
    .withMessage('Priority must be a non-negative integer.'),
];
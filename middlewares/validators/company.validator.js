const { query, body } = require('express-validator');
const { headerValidator } = require('./common.validator');

exports.getPaginatedCompaniesValidator = [
  ...headerValidator,
  query('categoryId').custom((value, { req }) => {
    const isAppRequest = req.originalUrl.includes('/app/');
    const isCmsRequest = req.originalUrl.includes('/cms/');

    if (isAppRequest) {
      if (req.originalUrl.includes('/get-all')) {
        return true;
      }

      if (!value) {
        throw new Error('Please provide category.');
      }
      const intVal = parseInt(value, 10);
      if (isNaN(intVal) || intVal < 1) {
        throw new Error('Category Id must be greater than 0');
      }
    }

    if (isCmsRequest && value) {
      throw new Error('categoryId is not allowed in CMS requests');
    }

    return true;
  }),
  query('page', 'Please provide page number.')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page number must be greater than 0'),
  query('limit', 'Please provide limit number.')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Limit number must be positive'),
  query('sortBy', 'Please provide sortBy field.')
    .optional()
    .isString()
    .custom((value) => {
      if (value) {
        const validSortFields = [
          'id',
          'company_name',
          'created_at',
          'updated_at',
          'upvote_count',
          'downvote_count',
        ];
        if (!validSortFields.includes(value)) {
          throw new Error(
            `Invalid sortBy field. Allowed values: ${validSortFields.join(
              ', '
            )}`
          );
        }
      }
      return true;
    }),
  query('sortOrder', 'Please provide sortOrder field.')
    .optional()
    .isString()
    .isIn(['ASC', 'DESC'])
    .withMessage('sortOrder must be either ASC or DESC'),
  query('search', 'Please provide search term.').optional().isString(),
  query('lastDateTime')
    .trim()
    .optional()
    .isInt({ min: 1 })
    .withMessage('Must be a positive integer')
    .custom((value) => {
      const date = new Date(Number(value));
      if (isNaN(date.getTime())) {
        throw new Error('Invalid epoch milliseconds');
      }
      return true;
    }),
];

exports.getCompanyByIdValidator = [...headerValidator];

exports.getContactUpDownValidator = [
  ...headerValidator,
  body('companyId')
    .trim()
    .notEmpty()
    .isInt({ min: 1 })
    .withMessage('Provide valid company Id.'),
];

exports.assignCompanyPriorityValidator = [
  ...headerValidator,
  body('companyId')
    .trim()
    .notEmpty()
    .isInt({ min: 1 })
    .withMessage('Provide valid company Id.'),
  body('priority')
    .trim()
    .notEmpty()
    .isInt({ min: 1 })
    .withMessage('Provide valid priority.'),
];
